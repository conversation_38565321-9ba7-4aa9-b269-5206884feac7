export const locales = ['zh', 'ja'] as const;
export type Locale = (typeof locales)[number];

export const defaultLocale: Locale = 'zh';

// 语言字典类型
export interface Dictionary {
  nav: {
    home: string;
    services: string;
    about: string;
    contact: string;
  };
  hero: {
    title: string;
    subtitle: string;
    cta: string;
  };
  services: {
    title: string;
    subtitle: string;
    express: {
      title: string;
      description: string;
    };
    sea: {
      title: string;
      description: string;
    };
    air: {
      title: string;
      description: string;
    };
    warehouse: {
      title: string;
      description: string;
    };
  };
  about: {
    title: string;
    subtitle: string;
    description: string;
  };
  contact: {
    title: string;
    subtitle: string;
    phone: string;
    email: string;
    address: string;
  };
  footer: {
    company: string;
    services: string;
    contact: string;
    copyright: string;
  };
}

// 获取字典
export async function getDictionary(locale: Locale): Promise<Dictionary> {
  try {
    const dict = await import(`@/dictionaries/${locale}.json`);
    return dict.default;
  } catch (error) {
    // 如果找不到对应语言文件，返回默认语言
    const dict = await import(`@/dictionaries/${defaultLocale}.json`);
    return dict.default;
  }
}

// 验证语言代码
export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
}
