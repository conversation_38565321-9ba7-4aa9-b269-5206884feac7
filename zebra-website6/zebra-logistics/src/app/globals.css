@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Apple-style global styles */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  letter-spacing: -0.01em;
}

/* Apple-style smooth transitions */
.transition-apple {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Apple-style focus states */
.focus-apple:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 125, 250, 0.3);
}

/* Smooth image loading */
img {
  transition: opacity 0.3s ease-in-out;
}

/* Apple-style button hover effects */
.btn-apple {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateZ(0);
}

.btn-apple:hover {
  transform: translateY(-2px) translateZ(0);
}

/* Apple-style card hover effects */
.card-apple {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateZ(0);
}

.card-apple:hover {
  transform: translateY(-8px) translateZ(0);
}
