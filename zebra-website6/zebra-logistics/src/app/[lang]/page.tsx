import { getDictionary, type Locale } from '@/lib/i18n';
import Header from '@/components/Header';
import Hero from '@/components/Hero';
import Services from '@/components/Services';
import Footer from '@/components/Footer';

interface PageProps {
  params: Promise<{ lang: Locale }>;
}

export default async function HomePage({ params }: PageProps) {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return (
    <div className="min-h-screen">
      <Header dict={dict} lang={lang} />
      <main>
        <Hero dict={dict} lang={lang} />
        <Services dict={dict} />
        
        {/* About Section */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="lg:text-center">
              <h2 className="text-base text-blue-600 font-semibold tracking-wide uppercase">
                {dict.about.title}
              </h2>
              <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                {dict.about.subtitle}
              </p>
              <p className="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
                {dict.about.description}
              </p>
            </div>

            <div className="mt-16">
              <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
                <div className="text-center">
                  <div className="flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white mx-auto text-2xl">
                    🏆
                  </div>
                  <h3 className="mt-6 text-lg font-medium text-gray-900">专业经验</h3>
                  <p className="mt-2 text-base text-gray-500">
                    多年跨境物流经验，深度了解中日贸易流程
                  </p>
                </div>

                <div className="text-center">
                  <div className="flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white mx-auto text-2xl">
                    🚀
                  </div>
                  <h3 className="mt-6 text-lg font-medium text-gray-900">高效服务</h3>
                  <p className="mt-2 text-base text-gray-500">
                    快速响应客户需求，提供一站式物流解决方案
                  </p>
                </div>

                <div className="text-center">
                  <div className="flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white mx-auto text-2xl">
                    🛡️
                  </div>
                  <h3 className="mt-6 text-lg font-medium text-gray-900">安全保障</h3>
                  <p className="mt-2 text-base text-gray-500">
                    完善的保险体系，确保货物运输安全
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-blue-600">
          <div className="max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-extrabold text-white sm:text-4xl">
              <span className="block">准备开始您的物流之旅？</span>
            </h2>
            <p className="mt-4 text-lg leading-6 text-blue-200">
              联系我们获取专业的物流咨询服务，让您的货物安全快速到达目的地。
            </p>
            <a
              href={`/${lang}/contact`}
              className="mt-8 w-full inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-blue-50 sm:w-auto"
            >
              立即联系我们
            </a>
          </div>
        </section>
      </main>
      <Footer dict={dict} lang={lang} />
    </div>
  );
}
