import { locales, type Locale } from '@/lib/i18n';

export async function generateStaticParams() {
  return locales.map((locale) => ({ lang: locale }));
}

export default async function LangLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ lang: Locale }>;
}) {
  const { lang } = await params;
  
  return (
    <html lang={lang}>
      <body>{children}</body>
    </html>
  );
}
