import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { locales, defaultLocale, isValidLocale } from '@/lib/i18n';

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // 检查路径是否已经包含语言前缀
  const pathnameHasLocale = locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  if (pathnameHasLocale) return;

  // 获取用户首选语言
  const locale = getLocale(request);
  
  // 重定向到带语言前缀的路径
  request.nextUrl.pathname = `/${locale}${pathname}`;
  return NextResponse.redirect(request.nextUrl);
}

function getLocale(request: NextRequest): string {
  // 从Accept-Language头部获取用户首选语言
  const acceptLanguage = request.headers.get('accept-language');
  
  if (acceptLanguage) {
    // 简单的语言匹配逻辑
    if (acceptLanguage.includes('ja')) {
      return 'ja';
    }
    if (acceptLanguage.includes('zh')) {
      return 'zh';
    }
  }

  return defaultLocale;
}

export const config = {
  matcher: [
    // 跳过内部路径 (_next)
    '/((?!_next|api|favicon.ico|logo.png|.*\\.).*)',
  ],
};
