import Link from 'next/link';
import Image from 'next/image';
import { Dictionary, Locale } from '@/lib/i18n';
// Temporary inline SVG icons until Heroicons is properly configured

interface HeroProps {
  dict: Dictionary;
  lang: Locale;
}

export default function Hero({ dict, lang }: HeroProps) {
  return (
    <section className="relative bg-white overflow-hidden">
      {/* Background with subtle gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-gray-50/50 to-white"></div>

      <div className="relative max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-16 lg:gap-24 items-center min-h-screen py-20 lg:py-32">
          {/* Content Section */}
          <div className="px-6 sm:px-8 lg:px-12 space-y-12">
            {/* Main Headline - Apple style typography */}
            <div className="space-y-8">
              <h1 className="text-5xl sm:text-6xl lg:text-7xl font-light tracking-tight text-gray-900 leading-none">
                <span className="block font-extralight text-gray-600 text-3xl sm:text-4xl lg:text-5xl mb-4">
                  {lang === 'zh' ? '连接' : '接続'}
                </span>
                <span className="block font-semibold">
                  {lang === 'zh' ? '福州' : '福州'}
                </span>
                <span className="block font-light text-gray-600">
                  {lang === 'zh' ? '与' : 'と'}
                </span>
                <span className="block font-semibold">
                  {lang === 'zh' ? '日本' : '日本'}
                </span>
              </h1>

              <p className="text-xl sm:text-2xl font-light text-gray-600 leading-relaxed max-w-2xl">
                {dict.hero.subtitle}
              </p>
            </div>

            {/* CTA Buttons - Apple style */}
            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6">
              <Link
                href={`/${lang}/contact`}
                className="group inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white bg-black hover:bg-gray-800 rounded-full transition-all duration-300 ease-out transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                {dict.hero.cta}
                <svg className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>

              <Link
                href={`/${lang}/services`}
                className="group inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-black bg-white border border-gray-200 hover:border-gray-300 rounded-full transition-all duration-300 ease-out transform hover:scale-105 shadow-sm hover:shadow-md"
              >
                {lang === 'zh' ? '了解服务' : 'サービス詳細'}
                <svg className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
            </div>
          </div>

          {/* Image Section */}
          <div className="relative px-6 sm:px-8 lg:px-0">
            <div className="relative aspect-[4/3] lg:aspect-[3/4] rounded-3xl overflow-hidden shadow-2xl">
              <Image
                src="/hero-logistics-minimal.webp"
                alt={lang === 'zh' ? '福州到日本物流运输' : '福州から日本への物流輸送'}
                fill
                className="object-cover transition-transform duration-700 hover:scale-105"
                priority
                sizes="(max-width: 768px) 100vw, 50vw"
              />

              {/* Subtle overlay with minimal text */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent">
                <div className="absolute bottom-8 left-8 right-8">
                  <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
                    <div className="text-center">
                      <div className="text-2xl font-light text-gray-900 mb-2">
                        福州 ⇄ 日本
                      </div>
                      <div className="text-sm font-medium text-gray-600 uppercase tracking-wider">
                        {lang === 'zh' ? '专业跨境物流' : 'プロフェッショナル越境物流'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
