import Link from 'next/link';
import Image from 'next/image';
import { Dictionary, Locale } from '@/lib/i18n';

interface FooterProps {
  dict: Dictionary;
  lang: Locale;
}

export default function Footer({ dict, lang }: FooterProps) {
  return (
    <footer className="bg-gray-900">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
        <div className="xl:grid xl:grid-cols-3 xl:gap-8">
          <div className="space-y-8 xl:col-span-1">
            <div className="flex items-center">
              <Image
                src="/logo.png"
                alt="斑马物巢"
                width={40}
                height={40}
                className="h-10 w-auto"
              />
              <span className="ml-2 text-xl font-bold text-white">
                {dict.footer.company}
              </span>
            </div>
            <p className="text-gray-400 text-base">
              专业的福州到日本跨境物流服务提供商，致力于为客户提供安全、快速、可靠的国际物流解决方案。
            </p>
            <div className="flex space-x-6">
              <a href="#" className="text-gray-400 hover:text-gray-300">
                <span className="sr-only">微信</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.900 7.60.5.5-3.187-2.75-6.874-8.343-6.874z"/>
                </svg>
              </a>
              <a href="#" className="text-gray-400 hover:text-gray-300">
                <span className="sr-only">QQ</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </a>
            </div>
          </div>
          <div className="mt-12 grid grid-cols-2 gap-8 xl:mt-0 xl:col-span-2">
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">
                  {dict.footer.services}
                </h3>
                <ul className="mt-4 space-y-4">
                  <li>
                    <Link href={`/${lang}/services`} className="text-base text-gray-300 hover:text-white">
                      快递服务
                    </Link>
                  </li>
                  <li>
                    <Link href={`/${lang}/services`} className="text-base text-gray-300 hover:text-white">
                      海运服务
                    </Link>
                  </li>
                  <li>
                    <Link href={`/${lang}/services`} className="text-base text-gray-300 hover:text-white">
                      空运服务
                    </Link>
                  </li>
                  <li>
                    <Link href={`/${lang}/services`} className="text-base text-gray-300 hover:text-white">
                      仓储服务
                    </Link>
                  </li>
                </ul>
              </div>
              <div className="mt-12 md:mt-0">
                <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">
                  公司信息
                </h3>
                <ul className="mt-4 space-y-4">
                  <li>
                    <Link href={`/${lang}/about`} className="text-base text-gray-300 hover:text-white">
                      关于我们
                    </Link>
                  </li>
                  <li>
                    <Link href={`/${lang}/contact`} className="text-base text-gray-300 hover:text-white">
                      联系我们
                    </Link>
                  </li>
                  <li>
                    <a href="#" className="text-base text-gray-300 hover:text-white">
                      服务条款
                    </a>
                  </li>
                  <li>
                    <a href="#" className="text-base text-gray-300 hover:text-white">
                      隐私政策
                    </a>
                  </li>
                </ul>
              </div>
            </div>
            <div className="md:grid md:grid-cols-1 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">
                  {dict.footer.contact}
                </h3>
                <ul className="mt-4 space-y-4">
                  <li className="text-base text-gray-300">
                    <span className="font-medium">电话：</span>
                    <a href="tel:+86-591-12345678" className="hover:text-white">
                      +86-591-12345678
                    </a>
                  </li>
                  <li className="text-base text-gray-300">
                    <span className="font-medium">邮箱：</span>
                    <a href="mailto:<EMAIL>" className="hover:text-white">
                      <EMAIL>
                    </a>
                  </li>
                  <li className="text-base text-gray-300">
                    <span className="font-medium">地址：</span>
                    福州市仓山区物流园区
                  </li>
                  <li className="text-base text-gray-300">
                    <span className="font-medium">营业时间：</span>
                    周一至周六 9:00-18:00
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-12 border-t border-gray-700 pt-8">
          <p className="text-base text-gray-400 xl:text-center">
            {dict.footer.copyright}
          </p>
        </div>
      </div>
    </footer>
  );
}
