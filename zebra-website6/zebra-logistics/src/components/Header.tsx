'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Dictionary, Locale } from '@/lib/i18n';
// Temporary inline SVG icons until Heroicons is properly configured

interface HeaderProps {
  dict: Dictionary;
  lang: Locale;
}

export default function Header({ dict, lang }: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const switchLanguage = (newLang: Locale) => {
    const currentPath = window.location.pathname;
    const pathWithoutLang = currentPath.replace(/^\/[a-z]{2}/, '');
    window.location.href = `/${newLang}${pathWithoutLang}`;
  };

  return (
    <header className="bg-white/80 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href={`/${lang}`} className="flex items-center group">
              <div className="relative">
                <Image
                  src="/logo.png"
                  alt="斑马物巢"
                  width={44}
                  height={44}
                  className="h-11 w-auto transition-transform duration-300 group-hover:scale-105"
                />
              </div>
              <span className="ml-3 text-2xl font-light text-gray-900 tracking-tight">
                斑马物巢
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-12">
            <Link
              href={`/${lang}`}
              className="text-gray-700 hover:text-black px-4 py-2 text-base font-light transition-all duration-300 ease-out relative group"
            >
              {dict.nav.home}
              <span className="absolute bottom-0 left-4 right-4 h-0.5 bg-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out"></span>
            </Link>
            <Link
              href={`/${lang}/services`}
              className="text-gray-700 hover:text-black px-4 py-2 text-base font-light transition-all duration-300 ease-out relative group"
            >
              {dict.nav.services}
              <span className="absolute bottom-0 left-4 right-4 h-0.5 bg-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out"></span>
            </Link>
            <Link
              href={`/${lang}/about`}
              className="text-gray-700 hover:text-black px-4 py-2 text-base font-light transition-all duration-300 ease-out relative group"
            >
              {dict.nav.about}
              <span className="absolute bottom-0 left-4 right-4 h-0.5 bg-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out"></span>
            </Link>
            <Link
              href={`/${lang}/contact`}
              className="text-gray-700 hover:text-black px-4 py-2 text-base font-light transition-all duration-300 ease-out relative group"
            >
              {dict.nav.contact}
              <span className="absolute bottom-0 left-4 right-4 h-0.5 bg-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out"></span>
            </Link>
          </nav>

          {/* Language Switcher & Mobile Menu Button */}
          <div className="flex items-center space-x-6">
            {/* Language Switcher */}
            <div className="flex bg-gray-100 rounded-full p-1">
              <button
                onClick={() => switchLanguage('zh')}
                className={`px-4 py-2 text-sm font-medium rounded-full transition-all duration-300 ${
                  lang === 'zh'
                    ? 'bg-white text-black shadow-sm'
                    : 'text-gray-600 hover:text-black'
                }`}
              >
                中文
              </button>
              <button
                onClick={() => switchLanguage('ja')}
                className={`px-4 py-2 text-sm font-medium rounded-full transition-all duration-300 ${
                  lang === 'ja'
                    ? 'bg-white text-black shadow-sm'
                    : 'text-gray-600 hover:text-black'
                }`}
              >
                日本語
              </button>
            </div>

            {/* Mobile menu button */}
            <button
              onClick={toggleMenu}
              className="lg:hidden inline-flex items-center justify-center p-2 rounded-full text-gray-700 hover:text-black hover:bg-gray-100 transition-all duration-300"
            >
              {isMenuOpen ? (
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : (
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden">
            <div className="px-6 pt-4 pb-6 space-y-2 bg-white/95 backdrop-blur-md border-t border-gray-100">
              <Link
                href={`/${lang}`}
                className="text-gray-700 hover:text-black block px-4 py-3 text-lg font-light rounded-xl hover:bg-gray-50 transition-all duration-300"
                onClick={() => setIsMenuOpen(false)}
              >
                {dict.nav.home}
              </Link>
              <Link
                href={`/${lang}/services`}
                className="text-gray-700 hover:text-black block px-4 py-3 text-lg font-light rounded-xl hover:bg-gray-50 transition-all duration-300"
                onClick={() => setIsMenuOpen(false)}
              >
                {dict.nav.services}
              </Link>
              <Link
                href={`/${lang}/about`}
                className="text-gray-700 hover:text-black block px-4 py-3 text-lg font-light rounded-xl hover:bg-gray-50 transition-all duration-300"
                onClick={() => setIsMenuOpen(false)}
              >
                {dict.nav.about}
              </Link>
              <Link
                href={`/${lang}/contact`}
                className="text-gray-700 hover:text-black block px-4 py-3 text-lg font-light rounded-xl hover:bg-gray-50 transition-all duration-300"
                onClick={() => setIsMenuOpen(false)}
              >
                {dict.nav.contact}
              </Link>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
