import { Dictionary } from '@/lib/i18n';
import Image from 'next/image';
// Temporary inline SVG icons until Heroicons is properly configured

interface ServicesProps {
  dict: Dictionary;
}

export default function Services({ dict }: ServicesProps) {
  const services = [
    {
      icon: 'truck',
      image: '/delivery-minimal.webp',
      title: dict.services.express.title,
      description: dict.services.express.description,
      features: ['3-7天送达', '实时跟踪', '安全包装', '门到门服务'],
    },
    {
      icon: 'shopping',
      image: '/ecommerce-logistics-minimal.webp',
      title: dict.services.ecommerce.title,
      description: dict.services.ecommerce.description,
      features: ['跨境电商', '订单处理', '库存管理', '客户服务'],
    },
    {
      icon: 'plane',
      image: '/air-cargo-minimal.webp',
      title: dict.services.air.title,
      description: dict.services.air.description,
      features: ['快速时效', '安全可靠', '温控运输', '紧急配送'],
    },
    {
      icon: 'building',
      image: '/warehouse-minimal.webp',
      title: dict.services.warehouse.title,
      description: dict.services.warehouse.description,
      features: ['专业仓储', '库存管理', '分拣包装', '质量检验'],
    },
  ];

  const getIcon = (iconType: string) => {
    switch (iconType) {
      case 'truck':
        return (
          <svg className="w-8 h-8 text-gray-900" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'shopping':
        return (
          <svg className="w-8 h-8 text-gray-900" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 7a2 2 0 01-2 2H8a2 2 0 01-2-2L5 9z" />
          </svg>
        );
      case 'plane':
        return (
          <svg className="w-8 h-8 text-gray-900" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
        );
      case 'building':
        return (
          <svg className="w-8 h-8 text-gray-900" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <section className="py-24 lg:py-32 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12">
        {/* Section Header - Apple style */}
        <div className="text-center max-w-4xl mx-auto mb-20">
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-light tracking-tight text-gray-900 mb-6">
            {dict.services.title}
          </h2>
          <p className="text-xl sm:text-2xl font-light text-gray-600 leading-relaxed">
            {dict.services.subtitle}
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
          {services.map((service, index) => {
            return (
              <div
                key={index}
                className="group relative bg-white rounded-3xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-500 ease-out transform hover:-translate-y-2"
              >
                {/* Image Section */}
                <div className="relative aspect-[16/10] overflow-hidden">
                  <Image
                    src={service.image}
                    alt={service.title}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-105"
                    sizes="(max-width: 768px) 100vw, 50vw"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

                  {/* Icon Overlay */}
                  <div className="absolute top-6 left-6">
                    <div className="w-16 h-16 bg-white/90 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                      {getIcon(service.icon)}
                    </div>
                  </div>
                </div>

                {/* Content Section */}
                <div className="p-8 lg:p-10">
                  <h3 className="text-2xl lg:text-3xl font-light text-gray-900 mb-4">
                    {service.title}
                  </h3>
                  <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                    {service.description}
                  </p>

                  {/* Features List */}
                  <ul className="space-y-4">
                    {service.features.map((feature, featureIndex) => (
                      <li
                        key={featureIndex}
                        className="flex items-center text-gray-700"
                      >
                        <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                          <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-base font-medium">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
