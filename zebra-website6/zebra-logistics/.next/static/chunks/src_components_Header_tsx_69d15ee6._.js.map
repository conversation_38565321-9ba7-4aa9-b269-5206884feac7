{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/zebra-website6/zebra-logistics/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Dictionary, Locale } from '@/lib/i18n';\n// Temporary inline SVG icons until Heroicons is properly configured\n\ninterface HeaderProps {\n  dict: Dictionary;\n  lang: Locale;\n}\n\nexport default function Header({ dict, lang }: HeaderProps) {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  const switchLanguage = (newLang: Locale) => {\n    const currentPath = window.location.pathname;\n    const pathWithoutLang = currentPath.replace(/^\\/[a-z]{2}/, '');\n    window.location.href = `/${newLang}${pathWithoutLang}`;\n  };\n\n  return (\n    <header className=\"bg-white/80 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12\">\n        <div className=\"flex justify-between items-center h-20\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href={`/${lang}`} className=\"flex items-center group\">\n              <div className=\"relative\">\n                <Image\n                  src=\"/logo.png\"\n                  alt=\"斑马物巢\"\n                  width={44}\n                  height={44}\n                  className=\"h-11 w-auto transition-transform duration-300 group-hover:scale-105\"\n                />\n              </div>\n              <span className=\"ml-3 text-2xl font-light text-gray-900 tracking-tight\">\n                斑马物巢\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center space-x-12\">\n            <Link\n              href={`/${lang}`}\n              className=\"text-gray-700 hover:text-black px-4 py-2 text-base font-light transition-all duration-300 ease-out relative group\"\n            >\n              {dict.nav.home}\n              <span className=\"absolute bottom-0 left-4 right-4 h-0.5 bg-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out\"></span>\n            </Link>\n            <Link\n              href={`/${lang}/services`}\n              className=\"text-gray-700 hover:text-black px-4 py-2 text-base font-light transition-all duration-300 ease-out relative group\"\n            >\n              {dict.nav.services}\n              <span className=\"absolute bottom-0 left-4 right-4 h-0.5 bg-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out\"></span>\n            </Link>\n            <Link\n              href={`/${lang}/about`}\n              className=\"text-gray-700 hover:text-black px-4 py-2 text-base font-light transition-all duration-300 ease-out relative group\"\n            >\n              {dict.nav.about}\n              <span className=\"absolute bottom-0 left-4 right-4 h-0.5 bg-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out\"></span>\n            </Link>\n            <Link\n              href={`/${lang}/contact`}\n              className=\"text-gray-700 hover:text-black px-4 py-2 text-base font-light transition-all duration-300 ease-out relative group\"\n            >\n              {dict.nav.contact}\n              <span className=\"absolute bottom-0 left-4 right-4 h-0.5 bg-black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out\"></span>\n            </Link>\n          </nav>\n\n          {/* Language Switcher & Mobile Menu Button */}\n          <div className=\"flex items-center space-x-6\">\n            {/* Language Switcher */}\n            <div className=\"flex bg-gray-100 rounded-full p-1\">\n              <button\n                onClick={() => switchLanguage('zh')}\n                className={`px-4 py-2 text-sm font-medium rounded-full transition-all duration-300 ${\n                  lang === 'zh'\n                    ? 'bg-white text-black shadow-sm'\n                    : 'text-gray-600 hover:text-black'\n                }`}\n              >\n                中文\n              </button>\n              <button\n                onClick={() => switchLanguage('ja')}\n                className={`px-4 py-2 text-sm font-medium rounded-full transition-all duration-300 ${\n                  lang === 'ja'\n                    ? 'bg-white text-black shadow-sm'\n                    : 'text-gray-600 hover:text-black'\n                }`}\n              >\n                日本語\n              </button>\n            </div>\n\n            {/* Mobile menu button */}\n            <button\n              onClick={toggleMenu}\n              className=\"lg:hidden inline-flex items-center justify-center p-2 rounded-full text-gray-700 hover:text-black hover:bg-gray-100 transition-all duration-300\"\n            >\n              {isMenuOpen ? (\n                <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              ) : (\n                <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"lg:hidden\">\n            <div className=\"px-6 pt-4 pb-6 space-y-2 bg-white/95 backdrop-blur-md border-t border-gray-100\">\n              <Link\n                href={`/${lang}`}\n                className=\"text-gray-700 hover:text-black block px-4 py-3 text-lg font-light rounded-xl hover:bg-gray-50 transition-all duration-300\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {dict.nav.home}\n              </Link>\n              <Link\n                href={`/${lang}/services`}\n                className=\"text-gray-700 hover:text-black block px-4 py-3 text-lg font-light rounded-xl hover:bg-gray-50 transition-all duration-300\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {dict.nav.services}\n              </Link>\n              <Link\n                href={`/${lang}/about`}\n                className=\"text-gray-700 hover:text-black block px-4 py-3 text-lg font-light rounded-xl hover:bg-gray-50 transition-all duration-300\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {dict.nav.about}\n              </Link>\n              <Link\n                href={`/${lang}/contact`}\n                className=\"text-gray-700 hover:text-black block px-4 py-3 text-lg font-light rounded-xl hover:bg-gray-50 transition-all duration-300\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {dict.nav.contact}\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAae,SAAS,OAAO,EAAE,IAAI,EAAE,IAAI,EAAe;;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;QAC5C,MAAM,kBAAkB,YAAY,OAAO,CAAC,eAAe;QAC3D,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,UAAU,iBAAiB;IACxD;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,CAAC,EAAE,MAAM;gCAAE,WAAU;;kDAChC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;kDAGd,6LAAC;wCAAK,WAAU;kDAAwD;;;;;;;;;;;;;;;;;sCAO5E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,CAAC,EAAE,MAAM;oCAChB,WAAU;;wCAET,KAAK,GAAG,CAAC,IAAI;sDACd,6LAAC;4CAAK,WAAU;;;;;;;;;;;;8CAElB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC;oCACzB,WAAU;;wCAET,KAAK,GAAG,CAAC,QAAQ;sDAClB,6LAAC;4CAAK,WAAU;;;;;;;;;;;;8CAElB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC;oCACtB,WAAU;;wCAET,KAAK,GAAG,CAAC,KAAK;sDACf,6LAAC;4CAAK,WAAU;;;;;;;;;;;;8CAElB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC;oCACxB,WAAU;;wCAET,KAAK,GAAG,CAAC,OAAO;sDACjB,6LAAC;4CAAK,WAAU;;;;;;;;;;;;;;;;;;sCAKpB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAW,CAAC,uEAAuE,EACjF,SAAS,OACL,kCACA,kCACJ;sDACH;;;;;;sDAGD,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAW,CAAC,uEAAuE,EACjF,SAAS,OACL,kCACA,kCACJ;sDACH;;;;;;;;;;;;8CAMH,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAET,2BACC,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC9D,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;6DAGvE,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC9D,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQ9E,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,MAAM;gCAChB,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,KAAK,GAAG,CAAC,IAAI;;;;;;0CAEhB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC;gCACzB,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,KAAK,GAAG,CAAC,QAAQ;;;;;;0CAEpB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC;gCACtB,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,KAAK,GAAG,CAAC,KAAK;;;;;;0CAEjB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC;gCACxB,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,KAAK,GAAG,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjC;GArJwB;KAAA", "debugId": null}}]}