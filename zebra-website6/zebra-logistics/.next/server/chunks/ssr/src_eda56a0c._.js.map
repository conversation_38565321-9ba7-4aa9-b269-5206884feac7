{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/zebra-website6/zebra-logistics/src/lib/i18n.ts"], "sourcesContent": ["export const locales = ['zh', 'ja'] as const;\nexport type Locale = (typeof locales)[number];\n\nexport const defaultLocale: Locale = 'zh';\n\n// 语言字典类型\nexport interface Dictionary {\n  nav: {\n    home: string;\n    services: string;\n    about: string;\n    contact: string;\n  };\n  hero: {\n    title: string;\n    subtitle: string;\n    cta: string;\n  };\n  services: {\n    title: string;\n    subtitle: string;\n    express: {\n      title: string;\n      description: string;\n    };\n    ecommerce: {\n      title: string;\n      description: string;\n    };\n    air: {\n      title: string;\n      description: string;\n    };\n    warehouse: {\n      title: string;\n      description: string;\n    };\n  };\n  about: {\n    title: string;\n    subtitle: string;\n    description: string;\n  };\n  contact: {\n    title: string;\n    subtitle: string;\n    phone: string;\n    email: string;\n    address: string;\n  };\n  footer: {\n    company: string;\n    services: string;\n    contact: string;\n    copyright: string;\n  };\n}\n\n// 获取字典\nexport async function getDictionary(locale: Locale): Promise<Dictionary> {\n  try {\n    const dict = await import(`@/dictionaries/${locale}.json`);\n    return dict.default;\n  } catch (error) {\n    // 如果找不到对应语言文件，返回默认语言\n    const dict = await import(`@/dictionaries/${defaultLocale}.json`);\n    return dict.default;\n  }\n}\n\n// 验证语言代码\nexport function isValidLocale(locale: string): locale is Locale {\n  return locales.includes(locale as Locale);\n}\n"], "names": [], "mappings": ";;;;;;AAAO,MAAM,UAAU;IAAC;IAAM;CAAK;AAG5B,MAAM,gBAAwB;AAwD9B,eAAe,cAAc,MAAc;IAChD,IAAI;QACF,MAAM,OAAO;;;;;;;;;kBAAa,CAAC,eAAe,EAAE,OAAO,KAAK,CAAC;QACzD,OAAO,KAAK,OAAO;IACrB,EAAE,OAAO,OAAO;QACd,qBAAqB;QACrB,MAAM,OAAO;QACb,OAAO,KAAK,OAAO;IACrB;AACF;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,QAAQ,QAAQ,CAAC;AAC1B", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/zebra-website6/zebra-logistics/src/app/%5Blang%5D/layout.tsx"], "sourcesContent": ["import { locales, type Locale } from '@/lib/i18n';\n\nexport async function generateStaticParams() {\n  return locales.map((locale) => ({ lang: locale }));\n}\n\nexport default async function LangLayout({\n  children,\n  params,\n}: {\n  children: React.ReactNode;\n  params: Promise<{ lang: Locale }>;\n}) {\n  const { lang } = await params;\n  \n  return (\n    <html lang={lang}>\n      <body>{children}</body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEO,eAAe;IACpB,OAAO,kHAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,SAAW,CAAC;YAAE,MAAM;QAAO,CAAC;AAClD;AAEe,eAAe,WAAW,EACvC,QAAQ,EACR,MAAM,EAIP;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IAEvB,qBACE,8OAAC;QAAK,MAAM;kBACV,cAAA,8OAAC;sBAAM;;;;;;;;;;;AAGb", "debugId": null}}]}