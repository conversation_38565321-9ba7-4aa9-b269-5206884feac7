{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/zebra-website6/zebra-logistics/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/zebra-website6/zebra-logistics/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/zebra-website6/zebra-logistics/src/components/Hero.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport Image from 'next/image';\nimport { Dictionary, Locale } from '@/lib/i18n';\n// Temporary inline SVG icons until Heroicons is properly configured\n\ninterface HeroProps {\n  dict: Dictionary;\n  lang: Locale;\n}\n\nexport default function Hero({ dict, lang }: HeroProps) {\n  return (\n    <section className=\"relative bg-white overflow-hidden\">\n      {/* Background with subtle gradient */}\n      <div className=\"absolute inset-0 bg-gradient-to-b from-gray-50/50 to-white\"></div>\n\n      <div className=\"relative max-w-7xl mx-auto\">\n        <div className=\"grid lg:grid-cols-2 gap-16 lg:gap-24 items-center min-h-screen py-20 lg:py-32\">\n          {/* Content Section */}\n          <div className=\"px-6 sm:px-8 lg:px-12 space-y-12\">\n            {/* Main Headline - Apple style typography */}\n            <div className=\"space-y-8\">\n              <h1 className=\"text-5xl sm:text-6xl lg:text-7xl font-light tracking-tight text-gray-900 leading-none\">\n                <span className=\"block font-extralight text-gray-600 text-3xl sm:text-4xl lg:text-5xl mb-4\">\n                  {lang === 'zh' ? '连接' : '接続'}\n                </span>\n                <span className=\"block font-semibold\">\n                  {lang === 'zh' ? '福州' : '福州'}\n                </span>\n                <span className=\"block font-light text-gray-600\">\n                  {lang === 'zh' ? '与' : 'と'}\n                </span>\n                <span className=\"block font-semibold\">\n                  {lang === 'zh' ? '日本' : '日本'}\n                </span>\n              </h1>\n\n              <p className=\"text-xl sm:text-2xl font-light text-gray-600 leading-relaxed max-w-2xl\">\n                {dict.hero.subtitle}\n              </p>\n            </div>\n\n            {/* CTA Buttons - Apple style */}\n            <div className=\"flex flex-col sm:flex-row gap-4 sm:gap-6\">\n              <Link\n                href={`/${lang}/contact`}\n                className=\"group inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white bg-black hover:bg-gray-800 rounded-full transition-all duration-300 ease-out transform hover:scale-105 shadow-lg hover:shadow-xl\"\n              >\n                {dict.hero.cta}\n                <svg className=\"ml-2 h-5 w-5 transition-transform group-hover:translate-x-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7l5 5m0 0l-5 5m5-5H6\" />\n                </svg>\n              </Link>\n\n              <Link\n                href={`/${lang}/services`}\n                className=\"group inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-black bg-white border border-gray-200 hover:border-gray-300 rounded-full transition-all duration-300 ease-out transform hover:scale-105 shadow-sm hover:shadow-md\"\n              >\n                {lang === 'zh' ? '了解服务' : 'サービス詳細'}\n                <svg className=\"ml-2 h-5 w-5 transition-transform group-hover:translate-x-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7l5 5m0 0l-5 5m5-5H6\" />\n                </svg>\n              </Link>\n            </div>\n          </div>\n\n          {/* Image Section */}\n          <div className=\"relative px-6 sm:px-8 lg:px-0\">\n            <div className=\"relative aspect-[4/3] lg:aspect-[3/4] rounded-3xl overflow-hidden shadow-2xl\">\n              <Image\n                src=\"/hero-logistics-minimal.webp\"\n                alt={lang === 'zh' ? '福州到日本物流运输' : '福州から日本への物流輸送'}\n                fill\n                className=\"object-cover transition-transform duration-700 hover:scale-105\"\n                priority\n                sizes=\"(max-width: 768px) 100vw, 50vw\"\n              />\n\n              {/* Subtle overlay with minimal text */}\n              <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent\">\n                <div className=\"absolute bottom-8 left-8 right-8\">\n                  <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg\">\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl font-light text-gray-900 mb-2\">\n                        福州 ⇄ 日本\n                      </div>\n                      <div className=\"text-sm font-medium text-gray-600 uppercase tracking-wider\">\n                        {lang === 'zh' ? '专业跨境物流' : 'プロフェッショナル越境物流'}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASe,SAAS,KAAK,EAAE,IAAI,EAAE,IAAI,EAAa;IACpD,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DACb,SAAS,OAAO,OAAO;;;;;;8DAE1B,8OAAC;oDAAK,WAAU;8DACb,SAAS,OAAO,OAAO;;;;;;8DAE1B,8OAAC;oDAAK,WAAU;8DACb,SAAS,OAAO,MAAM;;;;;;8DAEzB,8OAAC;oDAAK,WAAU;8DACb,SAAS,OAAO,OAAO;;;;;;;;;;;;sDAI5B,8OAAC;4CAAE,WAAU;sDACV,KAAK,IAAI,CAAC,QAAQ;;;;;;;;;;;;8CAKvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC;4CACxB,WAAU;;gDAET,KAAK,IAAI,CAAC,GAAG;8DACd,8OAAC;oDAAI,WAAU;oDAA8D,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;sDAIzE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC;4CACzB,WAAU;;gDAET,SAAS,OAAO,SAAS;8DAC1B,8OAAC;oDAAI,WAAU;oDAA8D,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAK,SAAS,OAAO,cAAc;wCACnC,IAAI;wCACJ,WAAU;wCACV,QAAQ;wCACR,OAAM;;;;;;kDAIR,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAyC;;;;;;sEAGxD,8OAAC;4DAAI,WAAU;sEACZ,SAAS,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYpD", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/zebra-website6/zebra-logistics/src/components/Services.tsx"], "sourcesContent": ["import { Dictionary } from '@/lib/i18n';\nimport Image from 'next/image';\n// Temporary inline SVG icons until Heroicons is properly configured\n\ninterface ServicesProps {\n  dict: Dictionary;\n}\n\nexport default function Services({ dict }: ServicesProps) {\n  const services = [\n    {\n      icon: 'truck',\n      image: '/delivery-minimal.webp',\n      title: dict.services.express.title,\n      description: dict.services.express.description,\n      features: ['3-7天送达', '实时跟踪', '安全包装', '门到门服务'],\n    },\n    {\n      icon: 'globe',\n      image: '/hero-logistics-minimal.webp',\n      title: dict.services.sea.title,\n      description: dict.services.sea.description,\n      features: ['经济实惠', '大批量运输', '专业报关', '仓储服务'],\n    },\n    {\n      icon: 'plane',\n      image: '/air-cargo-minimal.webp',\n      title: dict.services.air.title,\n      description: dict.services.air.description,\n      features: ['快速时效', '安全可靠', '温控运输', '紧急配送'],\n    },\n    {\n      icon: 'building',\n      image: '/warehouse-minimal.webp',\n      title: dict.services.warehouse.title,\n      description: dict.services.warehouse.description,\n      features: ['专业仓储', '库存管理', '分拣包装', '质量检验'],\n    },\n  ];\n\n  const getIcon = (iconType: string) => {\n    switch (iconType) {\n      case 'truck':\n        return (\n          <svg className=\"w-8 h-8 text-gray-900\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n        );\n      case 'globe':\n        return (\n          <svg className=\"w-8 h-8 text-gray-900\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n        );\n      case 'plane':\n        return (\n          <svg className=\"w-8 h-8 text-gray-900\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n          </svg>\n        );\n      case 'building':\n        return (\n          <svg className=\"w-8 h-8 text-gray-900\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n          </svg>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <section className=\"py-24 lg:py-32 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12\">\n        {/* Section Header - Apple style */}\n        <div className=\"text-center max-w-4xl mx-auto mb-20\">\n          <h2 className=\"text-4xl sm:text-5xl lg:text-6xl font-light tracking-tight text-gray-900 mb-6\">\n            {dict.services.title}\n          </h2>\n          <p className=\"text-xl sm:text-2xl font-light text-gray-600 leading-relaxed\">\n            {dict.services.subtitle}\n          </p>\n        </div>\n\n        {/* Services Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16\">\n          {services.map((service, index) => {\n            return (\n              <div\n                key={index}\n                className=\"group relative bg-white rounded-3xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-500 ease-out transform hover:-translate-y-2\"\n              >\n                {/* Image Section */}\n                <div className=\"relative aspect-[16/10] overflow-hidden\">\n                  <Image\n                    src={service.image}\n                    alt={service.title}\n                    fill\n                    className=\"object-cover transition-transform duration-700 group-hover:scale-105\"\n                    sizes=\"(max-width: 768px) 100vw, 50vw\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent\"></div>\n\n                  {/* Icon Overlay */}\n                  <div className=\"absolute top-6 left-6\">\n                    <div className=\"w-16 h-16 bg-white/90 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg\">\n                      {getIcon(service.icon)}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Content Section */}\n                <div className=\"p-8 lg:p-10\">\n                  <h3 className=\"text-2xl lg:text-3xl font-light text-gray-900 mb-4\">\n                    {service.title}\n                  </h3>\n                  <p className=\"text-lg text-gray-600 mb-8 leading-relaxed\">\n                    {service.description}\n                  </p>\n\n                  {/* Features List */}\n                  <ul className=\"space-y-4\">\n                    {service.features.map((feature, featureIndex) => (\n                      <li\n                        key={featureIndex}\n                        className=\"flex items-center text-gray-700\"\n                      >\n                        <div className=\"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0\">\n                          <svg className=\"w-4 h-4 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                            <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                          </svg>\n                        </div>\n                        <span className=\"text-base font-medium\">{feature}</span>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOe,SAAS,SAAS,EAAE,IAAI,EAAiB;IACtD,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,OAAO,KAAK,QAAQ,CAAC,OAAO,CAAC,KAAK;YAClC,aAAa,KAAK,QAAQ,CAAC,OAAO,CAAC,WAAW;YAC9C,UAAU;gBAAC;gBAAU;gBAAQ;gBAAQ;aAAQ;QAC/C;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO,KAAK,QAAQ,CAAC,GAAG,CAAC,KAAK;YAC9B,aAAa,KAAK,QAAQ,CAAC,GAAG,CAAC,WAAW;YAC1C,UAAU;gBAAC;gBAAQ;gBAAS;gBAAQ;aAAO;QAC7C;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO,KAAK,QAAQ,CAAC,GAAG,CAAC,KAAK;YAC9B,aAAa,KAAK,QAAQ,CAAC,GAAG,CAAC,WAAW;YAC1C,UAAU;gBAAC;gBAAQ;gBAAQ;gBAAQ;aAAO;QAC5C;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO,KAAK,QAAQ,CAAC,SAAS,CAAC,KAAK;YACpC,aAAa,KAAK,QAAQ,CAAC,SAAS,CAAC,WAAW;YAChD,UAAU;gBAAC;gBAAQ;gBAAQ;gBAAQ;aAAO;QAC5C;KACD;IAED,MAAM,UAAU,CAAC;QACf,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;oBAAwB,MAAK;oBAAO,SAAQ;oBAAY,QAAO;8BAC5E,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;oBAAwB,MAAK;oBAAO,SAAQ;oBAAY,QAAO;8BAC5E,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;oBAAwB,MAAK;oBAAO,SAAQ;oBAAY,QAAO;8BAC5E,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;oBAAwB,MAAK;oBAAO,SAAQ;oBAAY,QAAO;8BAC5E,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,KAAK,QAAQ,CAAC,KAAK;;;;;;sCAEtB,8OAAC;4BAAE,WAAU;sCACV,KAAK,QAAQ,CAAC,QAAQ;;;;;;;;;;;;8BAK3B,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,qBACE,8OAAC;4BAEC,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,QAAQ,KAAK;4CAClB,KAAK,QAAQ,KAAK;4CAClB,IAAI;4CACJ,WAAU;4CACV,OAAM;;;;;;sDAER,8OAAC;4CAAI,WAAU;;;;;;sDAGf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,QAAQ,IAAI;;;;;;;;;;;;;;;;;8CAM3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,8OAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAItB,8OAAC;4CAAG,WAAU;sDACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,8OAAC;oDAEC,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAyB,MAAK;gEAAe,SAAQ;0EAClE,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAqH,UAAS;;;;;;;;;;;;;;;;sEAG7J,8OAAC;4DAAK,WAAU;sEAAyB;;;;;;;mDARpC;;;;;;;;;;;;;;;;;2BAnCR;;;;;oBAkDX;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/zebra-website6/zebra-logistics/src/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport Image from 'next/image';\nimport { Dictionary, Locale } from '@/lib/i18n';\n\ninterface FooterProps {\n  dict: Dictionary;\n  lang: Locale;\n}\n\nexport default function Footer({ dict, lang }: FooterProps) {\n  return (\n    <footer className=\"bg-gray-900\">\n      <div className=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8\">\n        <div className=\"xl:grid xl:grid-cols-3 xl:gap-8\">\n          <div className=\"space-y-8 xl:col-span-1\">\n            <div className=\"flex items-center\">\n              <Image\n                src=\"/logo.png\"\n                alt=\"斑马物巢\"\n                width={40}\n                height={40}\n                className=\"h-10 w-auto\"\n              />\n              <span className=\"ml-2 text-xl font-bold text-white\">\n                {dict.footer.company}\n              </span>\n            </div>\n            <p className=\"text-gray-400 text-base\">\n              专业的福州到日本跨境物流服务提供商，致力于为客户提供安全、快速、可靠的国际物流解决方案。\n            </p>\n            <div className=\"flex space-x-6\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-gray-300\">\n                <span className=\"sr-only\">微信</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.900 7.60.5.5-3.187-2.75-6.874-8.343-6.874z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-gray-300\">\n                <span className=\"sr-only\">QQ</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n          <div className=\"mt-12 grid grid-cols-2 gap-8 xl:mt-0 xl:col-span-2\">\n            <div className=\"md:grid md:grid-cols-2 md:gap-8\">\n              <div>\n                <h3 className=\"text-sm font-semibold text-gray-400 tracking-wider uppercase\">\n                  {dict.footer.services}\n                </h3>\n                <ul className=\"mt-4 space-y-4\">\n                  <li>\n                    <Link href={`/${lang}/services`} className=\"text-base text-gray-300 hover:text-white\">\n                      快递服务\n                    </Link>\n                  </li>\n                  <li>\n                    <Link href={`/${lang}/services`} className=\"text-base text-gray-300 hover:text-white\">\n                      海运服务\n                    </Link>\n                  </li>\n                  <li>\n                    <Link href={`/${lang}/services`} className=\"text-base text-gray-300 hover:text-white\">\n                      空运服务\n                    </Link>\n                  </li>\n                  <li>\n                    <Link href={`/${lang}/services`} className=\"text-base text-gray-300 hover:text-white\">\n                      仓储服务\n                    </Link>\n                  </li>\n                </ul>\n              </div>\n              <div className=\"mt-12 md:mt-0\">\n                <h3 className=\"text-sm font-semibold text-gray-400 tracking-wider uppercase\">\n                  公司信息\n                </h3>\n                <ul className=\"mt-4 space-y-4\">\n                  <li>\n                    <Link href={`/${lang}/about`} className=\"text-base text-gray-300 hover:text-white\">\n                      关于我们\n                    </Link>\n                  </li>\n                  <li>\n                    <Link href={`/${lang}/contact`} className=\"text-base text-gray-300 hover:text-white\">\n                      联系我们\n                    </Link>\n                  </li>\n                  <li>\n                    <a href=\"#\" className=\"text-base text-gray-300 hover:text-white\">\n                      服务条款\n                    </a>\n                  </li>\n                  <li>\n                    <a href=\"#\" className=\"text-base text-gray-300 hover:text-white\">\n                      隐私政策\n                    </a>\n                  </li>\n                </ul>\n              </div>\n            </div>\n            <div className=\"md:grid md:grid-cols-1 md:gap-8\">\n              <div>\n                <h3 className=\"text-sm font-semibold text-gray-400 tracking-wider uppercase\">\n                  {dict.footer.contact}\n                </h3>\n                <ul className=\"mt-4 space-y-4\">\n                  <li className=\"text-base text-gray-300\">\n                    <span className=\"font-medium\">电话：</span>\n                    <a href=\"tel:+86-591-12345678\" className=\"hover:text-white\">\n                      +86-591-12345678\n                    </a>\n                  </li>\n                  <li className=\"text-base text-gray-300\">\n                    <span className=\"font-medium\">邮箱：</span>\n                    <a href=\"mailto:<EMAIL>\" className=\"hover:text-white\">\n                      <EMAIL>\n                    </a>\n                  </li>\n                  <li className=\"text-base text-gray-300\">\n                    <span className=\"font-medium\">地址：</span>\n                    福州市仓山区物流园区\n                  </li>\n                  <li className=\"text-base text-gray-300\">\n                    <span className=\"font-medium\">营业时间：</span>\n                    周一至周六 9:00-18:00\n                  </li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div className=\"mt-12 border-t border-gray-700 pt-8\">\n          <p className=\"text-base text-gray-400 xl:text-center\">\n            {dict.footer.copyright}\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQe,SAAS,OAAO,EAAE,IAAI,EAAE,IAAI,EAAe;IACxD,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDACb,KAAK,MAAM,CAAC,OAAO;;;;;;;;;;;;8CAGxB,8OAAC;oCAAE,WAAU;8CAA0B;;;;;;8CAGvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAKhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACX,KAAK,MAAM,CAAC,QAAQ;;;;;;8DAEvB,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;sEAIxF,8OAAC;sEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;sEAIxF,8OAAC;sEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;sEAIxF,8OAAC;sEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;;;;;;sDAM5F,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA+D;;;;;;8DAG7E,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;sEAIrF,8OAAC;sEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;sEAIvF,8OAAC;sEACC,cAAA,8OAAC;gEAAE,MAAK;gEAAI,WAAU;0EAA2C;;;;;;;;;;;sEAInE,8OAAC;sEACC,cAAA,8OAAC;gEAAE,MAAK;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOzE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DACX,KAAK,MAAM,CAAC,OAAO;;;;;;0DAEtB,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;0EAAc;;;;;;0EAC9B,8OAAC;gEAAE,MAAK;gEAAuB,WAAU;0EAAmB;;;;;;;;;;;;kEAI9D,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;0EAAc;;;;;;0EAC9B,8OAAC;gEAAE,MAAK;gEAAiC,WAAU;0EAAmB;;;;;;;;;;;;kEAIxE,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAU;;;;;;;kEAG1C,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQtD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCACV,KAAK,MAAM,CAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;AAMlC", "debugId": null}}, {"offset": {"line": 1147, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/zebra-website6/zebra-logistics/src/app/%5Blang%5D/page.tsx"], "sourcesContent": ["import { getDictionary, type Locale } from '@/lib/i18n';\nimport Header from '@/components/Header';\nimport Hero from '@/components/Hero';\nimport Services from '@/components/Services';\nimport Footer from '@/components/Footer';\n\ninterface PageProps {\n  params: Promise<{ lang: Locale }>;\n}\n\nexport default async function HomePage({ params }: PageProps) {\n  const { lang } = await params;\n  const dict = await getDictionary(lang);\n\n  return (\n    <div className=\"min-h-screen\">\n      <Header dict={dict} lang={lang} />\n      <main>\n        <Hero dict={dict} lang={lang} />\n        <Services dict={dict} />\n        \n        {/* About Section */}\n        <section className=\"py-16 bg-gray-50\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"lg:text-center\">\n              <h2 className=\"text-base text-blue-600 font-semibold tracking-wide uppercase\">\n                {dict.about.title}\n              </h2>\n              <p className=\"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl\">\n                {dict.about.subtitle}\n              </p>\n              <p className=\"mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto\">\n                {dict.about.description}\n              </p>\n            </div>\n\n            <div className=\"mt-16\">\n              <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\">\n                <div className=\"text-center\">\n                  <div className=\"flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white mx-auto text-2xl\">\n                    🏆\n                  </div>\n                  <h3 className=\"mt-6 text-lg font-medium text-gray-900\">专业经验</h3>\n                  <p className=\"mt-2 text-base text-gray-500\">\n                    多年跨境物流经验，深度了解中日贸易流程\n                  </p>\n                </div>\n\n                <div className=\"text-center\">\n                  <div className=\"flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white mx-auto text-2xl\">\n                    🚀\n                  </div>\n                  <h3 className=\"mt-6 text-lg font-medium text-gray-900\">高效服务</h3>\n                  <p className=\"mt-2 text-base text-gray-500\">\n                    快速响应客户需求，提供一站式物流解决方案\n                  </p>\n                </div>\n\n                <div className=\"text-center\">\n                  <div className=\"flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white mx-auto text-2xl\">\n                    🛡️\n                  </div>\n                  <h3 className=\"mt-6 text-lg font-medium text-gray-900\">安全保障</h3>\n                  <p className=\"mt-2 text-base text-gray-500\">\n                    完善的保险体系，确保货物运输安全\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"bg-blue-600\">\n          <div className=\"max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8\">\n            <h2 className=\"text-3xl font-extrabold text-white sm:text-4xl\">\n              <span className=\"block\">准备开始您的物流之旅？</span>\n            </h2>\n            <p className=\"mt-4 text-lg leading-6 text-blue-200\">\n              联系我们获取专业的物流咨询服务，让您的货物安全快速到达目的地。\n            </p>\n            <a\n              href={`/${lang}/contact`}\n              className=\"mt-8 w-full inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-blue-50 sm:w-auto\"\n            >\n              立即联系我们\n            </a>\n          </div>\n        </section>\n      </main>\n      <Footer dict={dict} lang={lang} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAMe,eAAe,SAAS,EAAE,MAAM,EAAa;IAC1D,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,gBAAa,AAAD,EAAE;IAEjC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;gBAAC,MAAM;gBAAM,MAAM;;;;;;0BAC1B,8OAAC;;kCACC,8OAAC,0HAAA,CAAA,UAAI;wBAAC,MAAM;wBAAM,MAAM;;;;;;kCACxB,8OAAC,8HAAA,CAAA,UAAQ;wBAAC,MAAM;;;;;;kCAGhB,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,KAAK,KAAK,CAAC,KAAK;;;;;;sDAEnB,8OAAC;4CAAE,WAAU;sDACV,KAAK,KAAK,CAAC,QAAQ;;;;;;sDAEtB,8OAAC;4CAAE,WAAU;sDACV,KAAK,KAAK,CAAC,WAAW;;;;;;;;;;;;8CAI3B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAgG;;;;;;kEAG/G,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,8OAAC;wDAAE,WAAU;kEAA+B;;;;;;;;;;;;0DAK9C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAgG;;;;;;kEAG/G,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,8OAAC;wDAAE,WAAU;kEAA+B;;;;;;;;;;;;0DAK9C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAgG;;;;;;kEAG/G,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,8OAAC;wDAAE,WAAU;kEAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUtD,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC;wCAAK,WAAU;kDAAQ;;;;;;;;;;;8CAE1B,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;8CAGpD,8OAAC;oCACC,MAAM,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC;oCACxB,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAMP,8OAAC,4HAAA,CAAA,UAAM;gBAAC,MAAM;gBAAM,MAAM;;;;;;;;;;;;AAGhC", "debugId": null}}]}