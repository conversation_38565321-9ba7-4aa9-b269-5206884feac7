{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__f312d770._.js", "server/edge/chunks/edge-wrapper_fac655c1.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|api|favicon.ico|logo.png|.*\\.).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|api|favicon.ico|logo.png|.*\\.).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "NcNMojRelajN6Y05jyDphdKgTML7f6AyhNqrlaENXjY=", "__NEXT_PREVIEW_MODE_ID": "d7bca977aad692e3ee8b83b331ce7c55", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d396334817497096c8c01f05229beaeb34f8641d4a1f9853f2bfcf2c7a79e016", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "582fd820cc12bb87f99a3d38846d4538a58d69707d85ac50d9f162dec55e4e86"}}}, "sortedMiddleware": ["/"], "functions": {}}