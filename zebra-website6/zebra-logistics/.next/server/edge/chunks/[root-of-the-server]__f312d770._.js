(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__f312d770._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/dictionaries/ja.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"nav\":{\"home\":\"ホーム\",\"services\":\"サービス\",\"about\":\"会社概要\",\"contact\":\"お問い合わせ\"},\"hero\":{\"title\":\"福州と日本を結ぶプロフェッショナル物流ブリッジ\",\"subtitle\":\"ゼブラロジスティクスは、安全で迅速、信頼性の高い越境物流サービスを提供し、お客様の貨物を目的地まで円滑にお届けします。\",\"cta\":\"今すぐお問い合わせ\"},\"services\":{\"title\":\"私たちのサービス\",\"subtitle\":\"プロフェッショナルな越境物流ソリューションで、様々な輸送ニーズにお応えします\",\"express\":{\"title\":\"宅配サービス\",\"description\":\"迅速で安全な小包輸送、3-7日で日本全国にお届け\"},\"sea\":{\"title\":\"海上輸送サービス\",\"description\":\"経済的な大量貨物輸送、バルク商品輸出に最適\"},\"air\":{\"title\":\"航空輸送サービス\",\"description\":\"時間効率の高い航空輸送、緊急貨物に最適な選択\"},\"warehouse\":{\"title\":\"倉庫サービス\",\"description\":\"プロフェッショナルな倉庫管理で、お客様の貨物の安全を保障\"}},\"about\":{\"title\":\"ゼブラロジスティクスについて\",\"subtitle\":\"プロフェッショナルな越境物流サービスプロバイダー\",\"description\":\"ゼブラロジスティクスは福州に設立され、中日越境物流サービスに特化しています。豊富な国際物流経験と完備されたサービスネットワークを持ち、お客様に効率的で安全、経済的な物流ソリューションを提供することに専念しています。\"},\"contact\":{\"title\":\"お問い合わせ\",\"subtitle\":\"いつでもプロフェッショナルな物流コンサルティングサービスを提供\",\"phone\":\"電話\",\"email\":\"メール\",\"address\":\"住所\"},\"footer\":{\"company\":\"ゼブラロジスティクス\",\"services\":\"サービス項目\",\"contact\":\"連絡先\",\"copyright\":\"著作権 © 2024 ゼブラロジスティクス株式会社\"}}"));}}),
"[project]/src/dictionaries/zh.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"nav\":{\"home\":\"首页\",\"services\":\"服务\",\"about\":\"关于我们\",\"contact\":\"联系我们\"},\"hero\":{\"title\":\"连接福州与日本的专业物流桥梁\",\"subtitle\":\"斑马物巢为您提供安全、快速、可靠的跨境物流服务，让您的货物畅通无阻地到达目的地。\",\"cta\":\"立即咨询\"},\"services\":{\"title\":\"我们的服务\",\"subtitle\":\"专业的跨境物流解决方案，满足您的各种运输需求\",\"express\":{\"title\":\"快递服务\",\"description\":\"快速安全的小包裹运输，3-7天直达日本各地\"},\"sea\":{\"title\":\"海运服务\",\"description\":\"经济实惠的大宗货物运输，适合批量商品出口\"},\"air\":{\"title\":\"空运服务\",\"description\":\"时效性强的航空运输，紧急货物的最佳选择\"},\"warehouse\":{\"title\":\"仓储服务\",\"description\":\"专业的仓储管理，为您的货物提供安全保障\"}},\"about\":{\"title\":\"关于斑马物巢\",\"subtitle\":\"专业的跨境物流服务提供商\",\"description\":\"斑马物巢成立于福州，专注于中日跨境物流服务。我们拥有丰富的国际物流经验和完善的服务网络，致力于为客户提供高效、安全、经济的物流解决方案。\"},\"contact\":{\"title\":\"联系我们\",\"subtitle\":\"随时为您提供专业的物流咨询服务\",\"phone\":\"电话\",\"email\":\"邮箱\",\"address\":\"地址\"},\"footer\":{\"company\":\"斑马物巢\",\"services\":\"服务项目\",\"contact\":\"联系方式\",\"copyright\":\"版权所有 © 2024 斑马物巢物流有限公司\"}}"));}}),
"[project]/src/lib/i18n.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "defaultLocale": (()=>defaultLocale),
    "getDictionary": (()=>getDictionary),
    "isValidLocale": (()=>isValidLocale),
    "locales": (()=>locales)
});
const locales = [
    'zh',
    'ja'
];
const defaultLocale = 'zh';
async function getDictionary(locale) {
    try {
        const dict = await __turbopack_context__.f({
            "@/dictionaries/ja.json": {
                id: ()=>"[project]/src/dictionaries/ja.json (json)",
                module: ()=>Promise.resolve().then(()=>__turbopack_context__.i("[project]/src/dictionaries/ja.json (json)"))
            },
            "@/dictionaries/zh.json": {
                id: ()=>"[project]/src/dictionaries/zh.json (json)",
                module: ()=>Promise.resolve().then(()=>__turbopack_context__.i("[project]/src/dictionaries/zh.json (json)"))
            }
        }).import(`@/dictionaries/${locale}.json`);
        return dict.default;
    } catch (error) {
        // 如果找不到对应语言文件，返回默认语言
        const dict = await Promise.resolve().then(()=>__turbopack_context__.i("[project]/src/dictionaries/zh.json (json)"));
        return dict.default;
    }
}
function isValidLocale(locale) {
    return locales.includes(locale);
}
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/i18n.ts [middleware-edge] (ecmascript)");
;
;
function middleware(request) {
    const pathname = request.nextUrl.pathname;
    // 检查路径是否已经包含语言前缀
    const pathnameHasLocale = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["locales"].some((locale)=>pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`);
    if (pathnameHasLocale) return;
    // 获取用户首选语言
    const locale = getLocale(request);
    // 重定向到带语言前缀的路径
    request.nextUrl.pathname = `/${locale}${pathname}`;
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(request.nextUrl);
}
function getLocale(request) {
    // 从Accept-Language头部获取用户首选语言
    const acceptLanguage = request.headers.get('accept-language');
    if (acceptLanguage) {
        // 简单的语言匹配逻辑
        if (acceptLanguage.includes('ja')) {
            return 'ja';
        }
        if (acceptLanguage.includes('zh')) {
            return 'zh';
        }
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["defaultLocale"];
}
const config = {
    matcher: [
        // 跳过内部路径 (_next)
        '/((?!_next|api|favicon.ico|logo.png|.*\\.).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__f312d770._.js.map