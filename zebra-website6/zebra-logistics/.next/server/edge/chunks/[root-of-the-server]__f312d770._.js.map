{"version": 3, "sources": [], "sections": [{"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/i18n.ts"], "sourcesContent": ["export const locales = ['zh', 'ja'] as const;\nexport type Locale = (typeof locales)[number];\n\nexport const defaultLocale: Locale = 'zh';\n\n// 语言字典类型\nexport interface Dictionary {\n  nav: {\n    home: string;\n    services: string;\n    about: string;\n    contact: string;\n  };\n  hero: {\n    title: string;\n    subtitle: string;\n    cta: string;\n  };\n  services: {\n    title: string;\n    subtitle: string;\n    express: {\n      title: string;\n      description: string;\n    };\n    sea: {\n      title: string;\n      description: string;\n    };\n    air: {\n      title: string;\n      description: string;\n    };\n    warehouse: {\n      title: string;\n      description: string;\n    };\n  };\n  about: {\n    title: string;\n    subtitle: string;\n    description: string;\n  };\n  contact: {\n    title: string;\n    subtitle: string;\n    phone: string;\n    email: string;\n    address: string;\n  };\n  footer: {\n    company: string;\n    services: string;\n    contact: string;\n    copyright: string;\n  };\n}\n\n// 获取字典\nexport async function getDictionary(locale: Locale): Promise<Dictionary> {\n  try {\n    const dict = await import(`@/dictionaries/${locale}.json`);\n    return dict.default;\n  } catch (error) {\n    // 如果找不到对应语言文件，返回默认语言\n    const dict = await import(`@/dictionaries/${defaultLocale}.json`);\n    return dict.default;\n  }\n}\n\n// 验证语言代码\nexport function isValidLocale(locale: string): locale is Locale {\n  return locales.includes(locale as Locale);\n}\n"], "names": [], "mappings": ";;;;;;AAAO,MAAM,UAAU;IAAC;IAAM;CAAK;AAG5B,MAAM,gBAAwB;AAwD9B,eAAe,cAAc,MAAc;IAChD,IAAI;QACF,MAAM,OAAO;;;;;;;;;kBAAa,CAAC,eAAe,EAAE,OAAO,KAAK,CAAC;QACzD,OAAO,KAAK,OAAO;IACrB,EAAE,OAAO,OAAO;QACd,qBAAqB;QACrB,MAAM,OAAO;QACb,OAAO,KAAK,OAAO;IACrB;AACF;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,QAAQ,QAAQ,CAAC;AAC1B"}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\nimport { locales, defaultLocale, isValidLocale } from '@/lib/i18n';\n\nexport function middleware(request: NextRequest) {\n  const pathname = request.nextUrl.pathname;\n\n  // 检查路径是否已经包含语言前缀\n  const pathnameHasLocale = locales.some(\n    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`\n  );\n\n  if (pathnameHasLocale) return;\n\n  // 获取用户首选语言\n  const locale = getLocale(request);\n  \n  // 重定向到带语言前缀的路径\n  request.nextUrl.pathname = `/${locale}${pathname}`;\n  return NextResponse.redirect(request.nextUrl);\n}\n\nfunction getLocale(request: NextRequest): string {\n  // 从Accept-Language头部获取用户首选语言\n  const acceptLanguage = request.headers.get('accept-language');\n  \n  if (acceptLanguage) {\n    // 简单的语言匹配逻辑\n    if (acceptLanguage.includes('ja')) {\n      return 'ja';\n    }\n    if (acceptLanguage.includes('zh')) {\n      return 'zh';\n    }\n  }\n\n  return defaultLocale;\n}\n\nexport const config = {\n  matcher: [\n    // 跳过内部路径 (_next)\n    '/((?!_next|api|favicon.ico|logo.png|.*\\\\.).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAEA;;;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ;IAEzC,iBAAiB;IACjB,MAAM,oBAAoB,0HAAA,CAAA,UAAO,CAAC,IAAI,CACpC,CAAC,SAAW,SAAS,UAAU,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,aAAa,CAAC,CAAC,EAAE,QAAQ;IAG7E,IAAI,mBAAmB;IAEvB,WAAW;IACX,MAAM,SAAS,UAAU;IAEzB,eAAe;IACf,QAAQ,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,SAAS,UAAU;IAClD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,QAAQ,OAAO;AAC9C;AAEA,SAAS,UAAU,OAAoB;IACrC,6BAA6B;IAC7B,MAAM,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAE3C,IAAI,gBAAgB;QAClB,YAAY;QACZ,IAAI,eAAe,QAAQ,CAAC,OAAO;YACjC,OAAO;QACT;QACA,IAAI,eAAe,QAAQ,CAAC,OAAO;YACjC,OAAO;QACT;IACF;IAEA,OAAO,0HAAA,CAAA,gBAAa;AACtB;AAEO,MAAM,SAAS;IACpB,SAAS;QACP,iBAAiB;QACjB;KACD;AACH"}}]}