{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__f312d770._.js", "server/edge/chunks/edge-wrapper_fac655c1.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next|api|favicon.ico|logo.png|.*\\.).*){(\\\\.json)}?", "originalSource": "/((?!_next|api|favicon.ico|logo.png|.*\\.).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "NcNMojRelajN6Y05jyDphdKgTML7f6AyhNqrlaENXjY=", "__NEXT_PREVIEW_MODE_ID": "09f73fbbeb84e222ac3be3d4ead672a3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "441bbf513ea470be934424055a441652006171f5f0557bfe0cd1223cace10fd6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2ff116e3c9edcb4a827dbf8b55948e6ddf1ffee124ef507398bbe50512ff9c17"}}}, "instrumentation": null, "functions": {}}